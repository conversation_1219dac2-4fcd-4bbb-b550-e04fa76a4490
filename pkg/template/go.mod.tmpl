module {{ .ModulePath }}

go 1.23

toolchain go1.23

// These are replace directives copied from porter
// When you use a newer version of <PERSON>, if you run into trouble with go mod tidy
// Copy any additional replace directives from <PERSON>'s go.mod file
// They must match the replaces used by porter everything to compile
replace github.com/spf13/viper => github.com/getporter/viper v1.7.1-porter.2.0.20210514172839-3ea827168363

require (
    get.porter.sh/magefiles v0.6.10
    get.porter.sh/porter v1.2.1
    github.com/ghodss/yaml v1.0.0
    github.com/spf13/cobra v1.8.1
    github.com/stretchr/testify v1.10.0
    github.com/xeipuuv/gojsonschema v1.2.0
    go.opentelemetry.io/otel v1.33.0
    gopkg.in/yaml.v2 v2.4.0
)