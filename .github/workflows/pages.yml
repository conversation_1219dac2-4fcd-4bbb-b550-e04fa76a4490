# GitHub Actions workflow for building and deploying the MkDocs site
# for the Porter Mixin Generator (skeletor) repo to GitHub Pages.
name: Deploy Documentation to GitHub Pages

on:
  release:
    types: [published] # Trigger when a release is published
  push:
    branches:
      - main # Deploy on push to main for latest docs
    paths:
      - 'docs/**'
      - 'mkdocs.yml'
      - '.github/workflows/pages.yml'
      - 'README.md'
  workflow_dispatch: # Allow manual trigger

permissions:
  contents: read # Need read access to checkout the repository
  pages: write # Need write access to deploy to GitHub Pages
  id-token: write # Needed for GitHub Pages deployment action

jobs:
  deploy-docs:
    name: Deploy Documentation
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for git info used by mkdocs-material

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install MkDocs and dependencies
        run: |
          pip install --upgrade pip
          pip install mkdocs mkdocs-material mkdocs-git-revision-date-localized-plugin mkdocs-git-committers-plugin-2

      - name: Build Skeletor binary
        run: |
          go mod tidy
          go build -o skeletor ./cmd/skeletor
          chmod +x skeletor

      # --- Prepare Documentation Source ---
      - name: Prepare documentation files
        run: |
          # Ensure docs directory exists
          mkdir -p docs

          # Copy README as index if docs/index.md doesn't exist
          if [ ! -f docs/index.md ]; then
            cp README.md docs/index.md
          fi

          # Copy other documentation files if they exist
          if [ -f CONTRIBUTING.md ]; then
            cp CONTRIBUTING.md docs/contributing.md
          fi

          if [ -f SECURITY.md ]; then
            cp SECURITY.md docs/security.md
          fi

          # Generate CLI help documentation
          if [ -f ./skeletor ] && [ -x ./skeletor ]; then
            echo "# CLI Help" > docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo "## Skeletor Help" >> docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
            ./skeletor --help >> docs/cli-help.md 2>/dev/null || echo "Error generating help" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo "## Create Command Help" >> docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
            ./skeletor create --help >> docs/cli-help.md 2>/dev/null || echo "Error generating create help" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo "## Version Information" >> docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
            ./skeletor version >> docs/cli-help.md 2>/dev/null || echo "Error generating version info" >> docs/cli-help.md
            echo '```' >> docs/cli-help.md
          else
            echo "# CLI Help" > docs/cli-help.md
            echo "" >> docs/cli-help.md
            echo "CLI help documentation will be generated when the binary is available." >> docs/cli-help.md
          fi

      # --- Build and Deploy ---
      - name: Build MkDocs site
        run: mkdocs build --config-file mkdocs.yml --site-dir _site --clean

      - name: Setup Pages
        uses: actions/configure-pages@v5

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: './_site'

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
