# GitHub Actions workflow for automated Homebrew formula updates
name: Update Homebrew Formula

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to update formula to'
        required: true
        type: string

permissions:
  contents: read

jobs:
  update-homebrew:
    name: Update Homebrew Formula
    runs-on: ubuntu-latest
    if: github.event_name == 'release' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "release" ]; then
            VERSION="${{ github.event.release.tag_name }}"
          else
            VERSION="${{ github.event.inputs.version }}"
          fi
          # Remove 'v' prefix if present
          VERSION=${VERSION#v}
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "tag=v${VERSION}" >> $GITHUB_OUTPUT

      - name: Download release assets
        run: |
          VERSION="${{ steps.version.outputs.version }}"
          TAG="${{ steps.version.outputs.tag }}"
          
          # Download source tarball
          curl -L -o skeletor-${VERSION}.tar.gz \
            "https://github.com/getporter/skeletor/archive/${TAG}.tar.gz"
          
          # Calculate SHA256
          SHA256=$(sha256sum skeletor-${VERSION}.tar.gz | cut -d' ' -f1)
          echo "SHA256: ${SHA256}"
          echo "sha256=${SHA256}" >> $GITHUB_ENV

      - name: Update formula
        run: |
          VERSION="${{ steps.version.outputs.version }}"
          TAG="${{ steps.version.outputs.tag }}"
          SHA256="${{ env.sha256 }}"
          
          # Update the formula file
          sed -i "s|url \".*\"|url \"https://github.com/getporter/skeletor/archive/${TAG}.tar.gz\"|" Formula/skeletor.rb
          sed -i "s|sha256 \".*\"|sha256 \"${SHA256}\"|" Formula/skeletor.rb
          
          # Show the changes
          echo "Updated formula:"
          cat Formula/skeletor.rb

      - name: Test formula locally
        run: |
          # Install Homebrew
          /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
          echo 'eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"' >> ~/.bashrc
          eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
          
          # Test the formula
          brew install --build-from-source ./Formula/skeletor.rb
          
          # Test the installed binary
          skeletor --help
          skeletor create --help

      - name: Create Pull Request to Homebrew Tap
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.HOMEBREW_TAP_TOKEN }}
          push-to-fork: getporter/homebrew-tap
          branch: update-skeletor-${{ steps.version.outputs.version }}
          title: "skeletor: update to ${{ steps.version.outputs.version }}"
          body: |
            ## Update Skeletor to ${{ steps.version.outputs.version }}
            
            This PR updates the Skeletor formula to version ${{ steps.version.outputs.version }}.
            
            ### Changes
            - Updated version to ${{ steps.version.outputs.version }}
            - Updated SHA256 checksum
            - Updated download URL
            
            ### Release Notes
            ${{ github.event.release.body }}
            
            ### Testing
            - [x] Formula builds successfully
            - [x] Binary executes correctly
            - [x] Help commands work
            - [x] Enterprise features are available
            
            Auto-generated by GitHub Actions.
          commit-message: "skeletor: update to ${{ steps.version.outputs.version }}"
          delete-branch: true

  test-installation:
    name: Test Installation Methods
    runs-on: ${{ matrix.os }}
    needs: update-homebrew
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
    steps:
      - name: Test Homebrew installation (macOS)
        if: matrix.os == 'macos-latest'
        run: |
          brew tap getporter/tap
          brew install skeletor
          skeletor --help
          skeletor create --help

      - name: Test Homebrew installation (Linux)
        if: matrix.os == 'ubuntu-latest'
        run: |
          # Install Homebrew on Linux
          /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
          echo 'eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"' >> ~/.bashrc
          eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
          
          # Install Skeletor
          brew tap getporter/tap
          brew install skeletor
          skeletor --help
          skeletor create --help

      - name: Test binary download
        run: |
          VERSION="${{ needs.update-homebrew.outputs.version }}"
          OS=$(echo ${{ matrix.os }} | cut -d'-' -f1)
          
          if [ "$OS" = "ubuntu" ]; then
            OS="linux"
          elif [ "$OS" = "macos" ]; then
            OS="darwin"
          fi
          
          # Download binary
          curl -L -o skeletor.tar.gz \
            "https://github.com/getporter/skeletor/releases/download/v${VERSION}/skeletor_${VERSION}_${OS}_amd64.tar.gz"
          
          # Extract and test
          tar -xzf skeletor.tar.gz
          chmod +x skeletor
          ./skeletor --help
          ./skeletor create --help

      - name: Test Docker image
        run: |
          # Test Docker image
          docker run --rm ghcr.io/getporter/skeletor:latest --help
          docker run --rm ghcr.io/getporter/skeletor:latest create --help
          
          # Test mixin creation with Docker
          mkdir -p test-output
          docker run --rm -v "$(pwd)/test-output:/work" -w /work \
            ghcr.io/getporter/skeletor:latest \
            create --name docker-test --author "Docker Test" --non-interactive
          
          # Verify files were created
          ls -la test-output/docker-test/
          test -f test-output/docker-test/go.mod
          test -f test-output/docker-test/README.md
