# MkDocs configuration for the Porter Mixin Generator documentation site.
site_name: Porter Mixin Generator (Skeletor)
site_description: Enterprise-grade command-line tool for generating Porter mixins with built-in security, compliance, authentication, and observability features.
site_url: https://mchorfa.github.io/porter-skeletor/
repo_url: https://github.com/MChorfa/porter-skeletor/
repo_name: MChorfa/porter-skeletor
edit_uri: edit/main/docs/

theme:
  name: material
  palette:
    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.top
    - navigation.tracking
    - toc.integrate
    - search.suggest
    - search.highlight
    - search.share
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
    - content.action.edit
    - content.action.view
    - announce.dismiss

# Define the navigation structure
nav:
  - Home: index.md
  - Getting Started:
    - Installation: installation.md
  - Enterprise Features:
    - Overview: enterprise-features.md
  - Reference:
    - Command Reference: command-reference.md
  - Examples: examples.md
  - CLI Help: cli-help.md

# Markdown extensions
markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - admonition
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.mark
  - attr_list
  - md_in_html
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

# Copyright (optional)
# copyright: Copyright &copy; The Porter Authors
