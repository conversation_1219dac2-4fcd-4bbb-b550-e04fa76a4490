# .goreleaser.yml
# Configuration for GoReleaser (https://goreleaser.com)

# Project metadata
project_name: skeletor

# Environment variables to pass to the build command
env:
  - CGO_ENABLED=0

# Build configuration
builds:
  # - # Build configuration for the skeletor mixin binary - SKIPPED as cmd/skeletor doesn't exist
  #   id: skeletor-mixin # Unique ID for this build
  #   main: ./cmd/skeletor # Path to the main package
  #   binary: skeletor # Output binary name
  #   ldflags: # Linker flags to reduce binary size and embed version info
  #     - -s -w
  #     # Example: Embed version info (requires version variable in main package)
  #     # - -X main.version={{.Version}}
  #     # - -X main.commit={{.Commit}}
  #     # - -X main.date={{.Date}}
  #     # - -X main.builtBy=goreleaser
  #   goos: # Target operating systems
  #     - linux
  #     - darwin
  #     - windows
  #   goarch: # Target architectures
  #     - amd64
  #     - arm64
  #   # Hooks to run before/after build (optional)
  #   # hooks:
  #   #   pre: mage some-pre-build-task
  #   #   post: mage some-post-build-task
  #   # SLSA Provenance is automatically generated by GoReleaser in supported CIs
  #   # when id-token: write permission is granted to the job.
  - # Build configuration for the skeletor tool
    id: generator-tool # Unique ID for this build
    main: ./cmd/skeletor # Path to the generator's main package
    binary: skeletor # Output binary name
    ldflags: # Linker flags
      - -s -w
      - -X main.Version={{.Version}}
      - -X main.Commit={{.Commit}}
      - -X main.Date={{.Date}}
    goos: # Target operating systems
      - linux
      - darwin
      - windows
    goarch: # Target architectures
      - amd64
      - arm64
    # SLSA Provenance is automatically generated by GoReleaser in supported CIs
    # when id-token: write permission is granted to the job.

# Archive configuration (creates .tar.gz and .zip files)
archives:
  # - # Archive configuration for the mixin binaries - SKIPPED
  #   id: skeletor-archives
  #   builds: # Reference the build ID defined above
  #     - skeletor-mixin
  #   name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
  #   format_overrides: # Use zip for windows
  #     - goos: windows
  #       format: zip
  #   files: # Include additional files in the archive (optional)
  #     - LICENSE
  #     - README.md
  #     - path/to/other/files/*
  - # Archive configuration for the generator tool binaries
    id: generator-archives
    builds: # Reference the generator build ID
      - generator-tool
    name_template: "skeletor_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    format_overrides: # Use zip for windows
      - goos: windows
        format: zip
    files: # Include additional files in the archive (optional)
      - LICENSE
      - README.md

# Checksum generation
checksum:
  name_template: 'checksums.txt'

# SBOM generation (CycloneDX and SPDX formats)
sboms:
  - # Generate SBOMs for built binaries (generator only)
    id: sbom-binaries # Optional ID
    artifacts: binary # Target binaries
    documents: # Output formats and filenames
      - "{{ .Binary }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}.cdx.json" # CycloneDX JSON
      - "{{ .Binary }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}.spdx.json" # SPDX JSON
  - # Generate SBOMs for Docker images (only generator tool image defined currently)
    id: sbom-docker # Optional ID
    artifacts: any # Target Docker images/manifests
    documents:
      - "{{ .ImageName }}.cdx.json" # CycloneDX JSON for image
      - "{{ .ImageName }}.spdx.json" # SPDX JSON for image

# Snapshot configuration (for testing releases without creating a tag)
snapshot:
  name_template: "{{ incpatch .Version }}-next"

# Changelog generation
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
      - '^chore:'
      - Merge pull request
      - Merge branch

# GitHub Release configuration
release:
  # Repo details are usually inferred from the git remote
  # github:
  #   owner: getporter
  #   name: skeletor
  draft: false # Set to true to create draft releases
  prerelease: auto # Automatically mark pre-releases based on tag format (e.g., v1.0.0-rc1)
  # Optional: Discuss commit messages for release notes
  # name_template: "{{.ProjectName}} {{.Tag}}"
  # Optional: Add extra files to the release
  # extra_files:
  #   - glob: ./path/to/extra/*

# Signing configuration (using Cosign)
# Requires id-token: write permission in workflow and cosign installation
# GoReleaser needs cosign installed in the environment where it runs (handled in Dagger pipeline)
signs:
  - # Sign archives and checksums using keyless signing (Fulcio/Rekor)
    id: cosign-keyless
    cmd: cosign
    env:
      # Enable experimental keyless signing support in Cosign
      - COSIGN_EXPERIMENTAL=1
    args:
      - "sign-blob"
      - "--output-signature=${signature}" # GoReleaser provides signature path
      - "--output-certificate=${certificate}" # GoReleaser provides certificate path
      - "${artifact}" # GoReleaser provides the artifact path
    artifacts: checksum # Sign checksums and archives by default
    output: true # Attach signature and certificate to the release

# Docker image configuration
dockers:
  - # Build Docker image for the skeletor tool
    ids: # Reference the generator build ID
      - generator-tool
    goos: linux # Target OS for the Docker image
    goarch: amd64 # Target architecture(s) - GoReleaser handles multi-arch builds if Dockerfile supports it
    # Docker image tags for GHCR
    image_templates:
      - "ghcr.io/getporter/skeletor:{{ .Version }}-amd64"
      - "ghcr.io/getporter/skeletor:latest-amd64" # Example: latest tag for amd64
    # Path to the Dockerfile (relative to project root) - Assumes Dockerfile exists at root (Task 5.1)
    dockerfile: Dockerfile
    build_flag_templates: # Build arguments for Dockerfile
      - "--label=org.opencontainers.image.created={{.Date}}"
      - "--label=org.opencontainers.image.title=skeletor"
      - "--label=org.opencontainers.image.description=Tool to generate Porter mixins"
      - "--label=org.opencontainers.image.url=https://github.com/getporter/skeletor"
      - "--label=org.opencontainers.image.source=https://github.com/getporter/skeletor"
      - "--label=org.opencontainers.image.revision={{.FullCommit}}"
      - "--label=org.opencontainers.image.version={{.Version}}"
      # Pass build args if needed by the Dockerfile
      # - "--build-arg=SOME_ARG={{ .Env.SOME_VALUE }}"
    # Push the image to GHCR (requires docker login in CI, often handled by GITHUB_TOKEN)
    use: buildx # Use docker buildx for multi-arch potential
    # push: true # Removed: Push is implicit during release
  - # Build Docker image for arm64
    ids:
      - generator-tool
    goos: linux
    goarch: arm64
    image_templates:
      - "ghcr.io/getporter/skeletor:{{ .Version }}-arm64"
      - "ghcr.io/getporter/skeletor:latest-arm64" # Example: latest tag for arm64
    dockerfile: Dockerfile
    build_flag_templates:
      - "--label=org.opencontainers.image.created={{.Date}}"
      - "--label=org.opencontainers.image.title=skeletor"
      - "--label=org.opencontainers.image.description=Tool to generate Porter mixins"
      - "--label=org.opencontainers.image.url=https://github.com/getporter/skeletor"
      - "--label=org.opencontainers.image.source=https://github.com/getporter/skeletor"
      - "--label=org.opencontainers.image.revision={{.FullCommit}}"
      - "--label=org.opencontainers.image.version={{.Version}}"
    use: buildx
    # push: true # Removed: Push is implicit during release
#   - # Build Docker image for the mixin (Example if needed)
#     ids:
#       - skeletor-mixin # Reference the mixin build ID
#     goos: linux
#     goarch: amd64
#     image_templates:
#       - "ghcr.io/getporter/skeletor:{{ .Version }}-amd64" # Example image for the mixin itself
#       - "ghcr.io/getporter/skeletor:latest-amd64"
#     dockerfile: templates/Dockerfile.tmpl # Use the template Dockerfile
#     build_flag_templates:
#       - "--label=org.opencontainers.image.created={{.Date}}"
#       - "--label=org.opencontainers.image.title={{.ProjectName}}" # Uses 'skeletor'
#       - "--label=org.opencontainers.image.revision={{.FullCommit}}"
#       - "--label=org.opencontainers.image.version={{.Version}}"
#       - "--build-arg=MIXIN_NAME={{ .ProjectName }}"
#     use: buildx
#     push: true

# Docker manifest configuration (creates multi-arch manifest lists)
docker_manifests:
  - # Manifest for the generator tool
    name_template: ghcr.io/getporter/skeletor:{{ .Version }}
    image_templates:
      - ghcr.io/getporter/skeletor:{{ .Version }}-amd64
      - ghcr.io/getporter/skeletor:{{ .Version }}-arm64
  - # Latest tag manifest for the generator tool
    name_template: ghcr.io/getporter/skeletor:latest
    image_templates:
      - ghcr.io/getporter/skeletor:latest-amd64
      - ghcr.io/getporter/skeletor:latest-arm64
  # - # Manifest for the mixin itself (Example if needed)
  #   name_template: ghcr.io/getporter/skeletor:{{ .Version }}
  #   image_templates:
  #     - ghcr.io/getporter/skeletor:{{ .Version }}-amd64
  #     - ghcr.io/getporter/skeletor:{{ .Version }}-arm64
  # - name_template: ghcr.io/getporter/skeletor:latest
  #   image_templates:
  #     - ghcr.io/getporter/skeletor:latest-amd64
  #     - ghcr.io/getporter/skeletor:latest-arm64

# Homebrew formula configuration
brews:
  - # Homebrew formula for the skeletor tool
    name: skeletor
    repository:
      owner: getporter
      name: homebrew-tap
      branch: main
      token: "{{ .Env.HOMEBREW_TAP_TOKEN }}"
    url_template: "https://github.com/getporter/skeletor/releases/download/{{ .Tag }}/{{ .ArtifactName }}"
    download_strategy: CurlDownloadStrategy
    commit_author:
      name: skeletor-bot
      email: <EMAIL>
    commit_msg_template: "Brew formula update for {{ .ProjectName }} version {{ .Tag }}"
    homepage: "https://getporter.github.io/skeletor/"
    description: "Enterprise-grade command-line tool for generating Porter mixins with built-in security, compliance, authentication, and observability features"
    license: "Apache-2.0"
    skip_upload: false
    dependencies:
      - name: go
        type: build
    install: |
      # Set build variables
      ldflags = %W[
        -s -w
        -X github.com/getporter/skeletor/pkg/version.Version=#{version}
        -X github.com/getporter/skeletor/pkg/version.Commit=#{Utils.git_head}
        -X github.com/getporter/skeletor/pkg/version.Date=#{time.iso8601}
      ]

      # Build the binary
      system "go", "build", *std_go_args(ldflags: ldflags), "./cmd/skeletor"

      # Install shell completions
      generate_completions_from_executable(bin/"skeletor", "completion")
    test: |
      # Test version output
      assert_match version.to_s, shell_output("#{bin}/skeletor version")

      # Test help output
      help_output = shell_output("#{bin}/skeletor --help")
      assert_match "Create new Porter mixins easily", help_output

      # Test create command help
      create_help = shell_output("#{bin}/skeletor create --help")
      assert_match "Create a new Porter mixin", create_help
      assert_match "--enable-security", create_help
      assert_match "--enable-compliance", create_help
      assert_match "--enable-auth", create_help
      assert_match "--enable-observability", create_help

      # Test dry run (should not create files)
      system bin/"skeletor", "create", "--name", "test-mixin", "--author", "Test Author", "--dry-run", "--non-interactive"
      refute_predicate testpath/"test-mixin", :exist?

      # Test actual mixin creation
      system bin/"skeletor", "create", "--name", "test-mixin", "--author", "Test Author", "--non-interactive"
      assert_predicate testpath/"test-mixin", :exist?
      assert_predicate testpath/"test-mixin/go.mod", :exist?
      assert_predicate testpath/"test-mixin/README.md", :exist?

      # Test enterprise features
      system bin/"skeletor", "create",
             "--name", "enterprise-mixin",
             "--author", "Enterprise Author",
             "--enable-security",
             "--security-features", "input_validation,rate_limiting",
             "--enable-compliance",
             "--compliance-frameworks", "soc2",
             "--enable-auth",
             "--auth-features", "rbac",
             "--enable-observability",
             "--observability-features", "apm",
             "--non-interactive"

      assert_predicate testpath/"enterprise-mixin", :exist?
      assert_predicate testpath/"enterprise-mixin/pkg/security", :exist?
      assert_predicate testpath/"enterprise-mixin/pkg/compliance", :exist?
      assert_predicate testpath/"enterprise-mixin/pkg/auth", :exist?
      assert_predicate testpath/"enterprise-mixin/pkg/observability", :exist?

# Before hook (runs before build)
before:
  hooks:
    - go mod tidy
    - go generate ./...
